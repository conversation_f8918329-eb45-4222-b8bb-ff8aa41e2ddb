#!/usr/bin/env python3
"""
Test script for the customer search API
Run this after starting the server to test the phone number search functionality
"""

import requests
import json

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_PHONE_NUMBERS = [
    "+919123456789",  # Your test number
    "9123456789",     # Your test number without +91
    "+919876543210",
    "9876543210",
    "************",
    "************"
]

def test_customer_search():
    """Test the customer search API with various phone number formats"""
    print("Testing Customer Search API")
    print("=" * 50)
    
    for phone in TEST_PHONE_NUMBERS:
        print(f"\nTesting phone number: {phone}")
        try:
            response = requests.get(
                f"{BASE_URL}/pos/v1/search_customer",
                params={"phone_number": phone},
                headers={"Authorization": "test-token"}  # You'll need a valid POS token
            )
            
            print(f"Status Code: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"Response: {json.dumps(data, indent=2)}")
            else:
                print(f"Error: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    test_customer_search()
