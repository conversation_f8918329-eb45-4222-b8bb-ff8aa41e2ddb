from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.connections.database import close_db_pool
from contextlib import asynccontextmanager
from fastapi.middleware.cors import CORSMiddleware
import os
from dotenv import load_dotenv

from firebase_admin import credentials
import firebase_admin

import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

# Initialize Sentry (must be done early, before other imports)
from app.config.sentry import init_sentry
init_sentry()

# Debug mode detection (DEBUG=false means production)
DEBUG = os.getenv("DEBUG", "true").lower() == "true"

logger.info(f"Running in {'debug' if DEBUG else 'production'} mode")

cred_app = credentials.Certificate("app/auth/firebase_app.json")
firebase_admin.initialize_app(cred_app, name="app")

cred_pos = credentials.Certificate("app/auth/firebase_pos.json")
firebase_admin.initialize_app(cred_pos, name="pos")


@asynccontextmanager
async def lifespan(_: FastAPI):
    logger.info("Starting Rozana OMS")
    yield
    logger.info("Shutting down Rozana OMS")
    close_db_pool()

# Disable docs in production (when DEBUG=false)
docs_url = "/docs" if DEBUG else None
redoc_url = "/redoc" if DEBUG else None

app = FastAPI(
    title="Rozana OMS", 
    version="4.0.0", 
    lifespan=lifespan,
    docs_url=docs_url,
    redoc_url=redoc_url
)

allowed_origins = os.getenv("ALLOWED_ORIGINS")
if allowed_origins:
   origins = [origin.strip() for origin in allowed_origins.split(",")]
else:
   origins = ["*"]

logger.info(f"Configuring CORS with allowed origins: {origins}")

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Middlewares
from app.middlewares.firebase_auth_app import FirebaseAuthMiddlewareAPP
from app.middlewares.firebase_auth_pos import FirebaseAuthMiddlewarePOS
app.add_middleware(FirebaseAuthMiddlewareAPP)
app.add_middleware(FirebaseAuthMiddlewarePOS)

# Register custom exception handlers
from app.middlewares.handlers import register_exception_handlers
register_exception_handlers(app)


# Routes
from app.routes.app import app_router
# from app.routes.web import web_router
from app.routes.pos import pos_router, customer_search_router
from app.routes.health import router as health_router
from app.routes.app.payments import payment_router
from app.routes.webhooks.razorpay_status import webhook_router

app.include_router(app_router, prefix="/app/v1")
app.include_router(payment_router, prefix="/app/v1")
# app.include_router(web_router, prefix="/web/v1")
app.include_router(pos_router, prefix="/pos/v1")
app.include_router(customer_search_router, prefix="/pos/v1")
app.include_router(health_router, tags=["health"])
app.include_router(webhook_router, prefix="/webhooks/v1")
