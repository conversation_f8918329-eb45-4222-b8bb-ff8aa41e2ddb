import logging
import firebase_admin
from firebase_admin import firestore
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class CustomerSearchService:
    def __init__(self):
        self.app_instance = firebase_admin.get_app("app")
        self.db = firestore.client(app=self.app_instance)

    async def search_customer_by_phone(self, phone_number: str) -> Optional[Dict[str, Any]]:
        """Search customer by phone number in Firebase custom-app project"""
        try:
            clean_phone = phone_number.strip().replace(" ", "").replace("-", "")

            # Search in Firebase users collection
            users_ref = self.db.collection('users')

            # Try multiple phone formats
            phone_formats = [
                clean_phone,
                f"+91{clean_phone}" if len(clean_phone) == 10 else clean_phone,
                clean_phone.replace("+91", "") if clean_phone.startswith("+91") else clean_phone
            ]

            for phone_format in phone_formats:
                query = users_ref.where('phoneNumber', '==', phone_format).limit(1)
                docs = query.stream()

                for doc in docs:
                    user_data = doc.to_dict()
                    return {
                        'customer_id': doc.id,
                        'customer_name': user_data.get('displayName', user_data.get('name', '')),
                        'phone_number': user_data.get('phoneNumber', ''),
                        'email': user_data.get('email', ''),
                        'address': self._extract_firebase_address(user_data),
                        'found': True,
                        'source': 'firebase'
                    }

            return {'found': False}

        except Exception as e:
            logger.error(f"Error searching Firebase for phone {phone_number}: {str(e)}")
            return {'found': False, 'error': str(e)}

    def _extract_firebase_address(self, user_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract address from Firebase user data"""
        try:
            # Check for address in user data
            if 'address' in user_data and user_data['address']:
                addr = user_data['address']
                return {
                    'full_name': user_data.get('displayName', user_data.get('name', '')),
                    'phone_number': user_data.get('phoneNumber', ''),
                    'address_line1': addr.get('street', addr.get('addressLine1', '')),
                    'address_line2': addr.get('apartment', addr.get('addressLine2', '')),
                    'city': addr.get('city', ''),
                    'state': addr.get('state', ''),
                    'postal_code': addr.get('zipCode', addr.get('postalCode', '')),
                    'country': 'india',
                    'type_of_address': 'home',
                    'latitude': addr.get('latitude'),
                    'longitude': addr.get('longitude')
                }

            # If no address object, create basic address from user data
            return {
                'full_name': user_data.get('displayName', user_data.get('name', '')),
                'phone_number': user_data.get('phoneNumber', ''),
                'address_line1': '',
                'address_line2': '',
                'city': '',
                'state': '',
                'postal_code': '',
                'country': 'india',
                'type_of_address': 'home',
                'latitude': None,
                'longitude': None
            }

        except Exception as e:
            logger.error(f"Error extracting Firebase address: {str(e)}")
            return None

customer_search_service = CustomerSearchService()
