import logging
from typing import Optional, Dict, Any
from app.connections.database import execute_raw_sql_readonly

logger = logging.getLogger(__name__)

class CustomerSearchService:

    async def search_customer_by_phone(self, phone_number: str) -> Optional[Dict[str, Any]]:
        """Search customer by phone number in local database"""
        try:
            # Clean phone number (remove spaces, dashes, etc.)
            clean_phone = phone_number.strip().replace(" ", "").replace("-", "")

            # Search in local database
            return await self._search_local_database(clean_phone)

        except Exception as e:
            logger.error(f"Error searching customer by phone {phone_number}: {str(e)}")
            return {'found': False, 'error': str(e)}

    async def _search_local_database(self, clean_phone: str) -> Dict[str, Any]:
        """Search customer in local PostgreSQL database from recent orders"""
        try:
            # Search for most recent order with this phone number
            sql = """
                SELECT DISTINCT ON (oa.phone_number)
                       o.customer_id, o.customer_name, oa.phone_number,
                       oa.full_name, oa.address_line1, oa.address_line2,
                       oa.city, oa.state, oa.postal_code, oa.country,
                       oa.type_of_address, oa.latitude, oa.longitude,
                       o.created_at
                FROM orders o
                JOIN order_addresses oa ON oa.order_id = o.id
                WHERE oa.phone_number = :phone_number
                   OR oa.phone_number = :phone_with_plus
                   OR oa.phone_number = :phone_clean
                ORDER BY oa.phone_number, o.created_at DESC
                LIMIT 1
            """

            params = {
                'phone_number': clean_phone,
                'phone_with_plus': f'+91{clean_phone}' if len(clean_phone) == 10 else f'+{clean_phone}',
                'phone_clean': clean_phone.replace('+91', '').replace('+', '')
            }

            rows = execute_raw_sql_readonly(sql, params)

            if rows:
                row = rows[0]
                return {
                    'customer_id': row['customer_id'],
                    'customer_name': row['customer_name'],
                    'phone_number': row['phone_number'],
                    'email': None,  # Not stored in orders table
                    'address': {
                        'full_name': row['full_name'],
                        'phone_number': row['phone_number'],
                        'address_line1': row['address_line1'],
                        'address_line2': row['address_line2'],
                        'city': row['city'],
                        'state': row['state'],
                        'postal_code': row['postal_code'],
                        'country': row['country'],
                        'type_of_address': row['type_of_address'],
                        'latitude': float(row['latitude']) if row['latitude'] else None,
                        'longitude': float(row['longitude']) if row['longitude'] else None
                    },
                    'found': True,
                    'source': 'local_database'
                }

            return {'found': False}

        except Exception as e:
            logger.error(f"Error searching local database: {str(e)}")
            return {'found': False}

customer_search_service = CustomerSearchService()
