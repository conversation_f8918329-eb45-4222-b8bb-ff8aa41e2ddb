import logging
import firebase_admin
from firebase_admin import firestore
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class FirebaseCustomerService:
    def __init__(self):
        self.app_instance = firebase_admin.get_app("app")
        self.db = firestore.client(app=self.app_instance)
    
    async def search_customer_by_phone(self, phone_number: str) -> Optional[Dict[str, Any]]:
        """Search customer by phone number in Firebase custom-app project"""
        try:
            # Clean phone number (remove spaces, dashes, etc.)
            clean_phone = phone_number.strip().replace(" ", "").replace("-", "")
            
            # Search in users collection by phone number
            users_ref = self.db.collection('users')
            query = users_ref.where('phoneNumber', '==', clean_phone).limit(1)
            docs = query.stream()
            
            for doc in docs:
                user_data = doc.to_dict()
                return {
                    'customer_id': doc.id,
                    'customer_name': user_data.get('displayName', ''),
                    'phone_number': user_data.get('phoneNumber', ''),
                    'email': user_data.get('email', ''),
                    'address': self._extract_address(user_data),
                    'found': True
                }
            
            return {'found': False}
            
        except Exception as e:
            logger.error(f"Error searching customer by phone {phone_number}: {str(e)}")
            return {'found': False, 'error': str(e)}
    
    def _extract_address(self, user_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract address information from user data"""
        try:
            # Check for address in user data
            if 'address' in user_data and user_data['address']:
                addr = user_data['address']
                return {
                    'full_name': user_data.get('displayName', ''),
                    'phone_number': user_data.get('phoneNumber', ''),
                    'address_line1': addr.get('street', ''),
                    'address_line2': addr.get('apartment', ''),
                    'city': addr.get('city', ''),
                    'state': addr.get('state', ''),
                    'postal_code': addr.get('zipCode', ''),
                    'country': 'india',
                    'type_of_address': 'home',
                    'latitude': addr.get('latitude'),
                    'longitude': addr.get('longitude')
                }
            return None
        except Exception as e:
            logger.error(f"Error extracting address: {str(e)}")
            return None

firebase_customer_service = FirebaseCustomerService()
