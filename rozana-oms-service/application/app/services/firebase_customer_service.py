import logging
import firebase_admin
from firebase_admin import firestore
from typing import Optional, Dict, Any
from app.connections.database import execute_raw_sql_readonly

logger = logging.getLogger(__name__)

class FirebaseCustomerService:
    def __init__(self):
        self.app_instance = firebase_admin.get_app("app")
        self.db = firestore.client(app=self.app_instance)

    async def search_customer_by_phone(self, phone_number: str) -> Optional[Dict[str, Any]]:
        """Search customer by phone number in both local database and Firebase"""
        try:
            # Clean phone number (remove spaces, dashes, etc.)
            clean_phone = phone_number.strip().replace(" ", "").replace("-", "")

            # First search in local database (recent orders)
            local_result = await self._search_local_database(clean_phone)
            if local_result.get('found'):
                return local_result

            # If not found locally, search in Firebase
            firebase_result = await self._search_firebase(clean_phone)
            return firebase_result

        except Exception as e:
            logger.error(f"Error searching customer by phone {phone_number}: {str(e)}")
            return {'found': False, 'error': str(e)}

    async def _search_local_database(self, clean_phone: str) -> Dict[str, Any]:
        """Search customer in local PostgreSQL database from recent orders"""
        try:
            # Search for most recent order with this phone number
            sql = """
                SELECT DISTINCT ON (oa.phone_number)
                       o.customer_id, o.customer_name, oa.phone_number,
                       oa.full_name, oa.address_line1, oa.address_line2,
                       oa.city, oa.state, oa.postal_code, oa.country,
                       oa.type_of_address, oa.latitude, oa.longitude,
                       o.created_at
                FROM orders o
                JOIN order_addresses oa ON oa.order_id = o.id
                WHERE oa.phone_number = :phone_number
                   OR oa.phone_number = :phone_with_plus
                   OR oa.phone_number = :phone_clean
                ORDER BY oa.phone_number, o.created_at DESC
                LIMIT 1
            """

            params = {
                'phone_number': clean_phone,
                'phone_with_plus': f'+91{clean_phone}' if len(clean_phone) == 10 else f'+{clean_phone}',
                'phone_clean': clean_phone.replace('+91', '').replace('+', '')
            }

            rows = execute_raw_sql_readonly(sql, params)

            if rows:
                row = rows[0]
                return {
                    'customer_id': row['customer_id'],
                    'customer_name': row['customer_name'],
                    'phone_number': row['phone_number'],
                    'email': None,  # Not stored in orders table
                    'address': {
                        'full_name': row['full_name'],
                        'phone_number': row['phone_number'],
                        'address_line1': row['address_line1'],
                        'address_line2': row['address_line2'],
                        'city': row['city'],
                        'state': row['state'],
                        'postal_code': row['postal_code'],
                        'country': row['country'],
                        'type_of_address': row['type_of_address'],
                        'latitude': float(row['latitude']) if row['latitude'] else None,
                        'longitude': float(row['longitude']) if row['longitude'] else None
                    },
                    'found': True,
                    'source': 'local_database'
                }

            return {'found': False}

        except Exception as e:
            logger.error(f"Error searching local database: {str(e)}")
            return {'found': False}

    async def _search_firebase(self, clean_phone: str) -> Dict[str, Any]:
        """Search customer in Firebase custom-app project"""
        try:
            # Search in users collection by phone number
            users_ref = self.db.collection('users')
            query = users_ref.where('phoneNumber', '==', clean_phone).limit(1)
            docs = query.stream()

            for doc in docs:
                user_data = doc.to_dict()
                return {
                    'customer_id': doc.id,
                    'customer_name': user_data.get('displayName', ''),
                    'phone_number': user_data.get('phoneNumber', ''),
                    'email': user_data.get('email', ''),
                    'address': self._extract_address(user_data),
                    'found': True,
                    'source': 'firebase'
                }

            return {'found': False}

        except Exception as e:
            logger.error(f"Error searching Firebase: {str(e)}")
            return {'found': False}
    
    def _extract_address(self, user_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract address information from user data"""
        try:
            # Check for address in user data
            if 'address' in user_data and user_data['address']:
                addr = user_data['address']
                return {
                    'full_name': user_data.get('displayName', ''),
                    'phone_number': user_data.get('phoneNumber', ''),
                    'address_line1': addr.get('street', ''),
                    'address_line2': addr.get('apartment', ''),
                    'city': addr.get('city', ''),
                    'state': addr.get('state', ''),
                    'postal_code': addr.get('zipCode', ''),
                    'country': 'india',
                    'type_of_address': 'home',
                    'latitude': addr.get('latitude'),
                    'longitude': addr.get('longitude')
                }
            return None
        except Exception as e:
            logger.error(f"Error extracting address: {str(e)}")
            return None

firebase_customer_service = FirebaseCustomerService()
