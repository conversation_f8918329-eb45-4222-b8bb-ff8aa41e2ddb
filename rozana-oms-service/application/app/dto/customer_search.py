from pydantic import BaseModel, Field
from typing import Optional

class CustomerAddress(BaseModel):
    full_name: str
    phone_number: str
    address_line1: str
    address_line2: Optional[str] = None
    city: str
    state: str
    postal_code: str
    country: str = "india"
    type_of_address: str = "home"
    latitude: Optional[float] = None
    longitude: Optional[float] = None

class CustomerSearchResponse(BaseModel):
    found: bool
    customer_id: Optional[str] = None
    customer_name: Optional[str] = None
    phone_number: Optional[str] = None
    email: Optional[str] = None
    address: Optional[CustomerAddress] = None
    error: Optional[str] = None
