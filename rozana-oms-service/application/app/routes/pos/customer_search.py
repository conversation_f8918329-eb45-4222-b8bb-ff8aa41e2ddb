from fastapi import APIRouter, Query, HTTPException
from app.dto.customer_search import CustomerSearchResponse, CustomerAddress
from app.services.firebase_customer_service import firebase_customer_service
import logging

logger = logging.getLogger(__name__)

customer_search_router = APIRouter(tags=["pos-customer"])

@customer_search_router.get("/search_customer", response_model=CustomerSearchResponse)
async def search_customer_by_phone(
    phone_number: str = Query(..., description="Customer phone number to search")
):
    """Search customer by phone number from Firebase custom-app project"""
    try:
        if not phone_number or len(phone_number.strip()) < 10:
            raise HTTPException(status_code=400, detail="Valid phone number required")
        
        result = await firebase_customer_service.search_customer_by_phone(phone_number)
        
        if result.get('found'):
            address_data = result.get('address')
            address = CustomerAddress(**address_data) if address_data else None

            return CustomerSearchResponse(
                found=True,
                customer_id=result.get('customer_id'),
                customer_name=result.get('customer_name'),
                phone_number=result.get('phone_number'),
                email=result.get('email'),
                address=address,
                source=result.get('source')
            )
        else:
            return CustomerSearchResponse(
                found=False,
                error=result.get('error')
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in customer search API: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
